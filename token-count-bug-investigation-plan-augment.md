# Token Count Display Bug - Investigation Results & Implementation Plan

## Root Cause Analysis

After examining the codebase, I've identified the core issue causing the "Counting..." display to persist indefinitely when line ranges are selected:

### The Problem: State Update Propagation Chain Break

The issue occurs in the data flow between content loading and component re-rendering:

1. **User selects line ranges** → `updateSelectedFile()` is called
2. **TreeItem detects partial selection** → `useEffect` triggers `loadFileContent()`
3. **Content loads successfully** → `updateFileWithContent()` updates `allFiles` state with `flushSync`
4. **TreeItem receives updated `node.fileData`** → BUT the memo comparison `areEqual()` may not detect the change
5. **Component doesn't re-render** → Token count calculation doesn't run → "Counting..." persists

### Key Findings

1. **File cards work correctly** because they:
   - Use a different data flow path
   - Access `fileData` directly from `allFiles` state
   - Don't rely on the TreeItem's memo optimization
   - Calculate tokens immediately when content is available

2. **TreeItem fails** because:
   - The `areEqual` memo comparison may not properly detect `fileData.content` changes
   - The `lineSelectedTokenCount` useMemo depends on `fileData?.content` but may not recalculate
   - The component doesn't re-render when content loads, so the token calculation never runs

3. **Token count appears after other actions** because:
   - Other actions (like folder selection) force a re-render
   - During that re-render, the content is now available and tokens are calculated

## Specific Technical Issues

### Issue 1: Memo Comparison Edge Case
The `areEqual` function checks for `fileData.content` changes (line 373), but there might be an edge case where:
- Content goes from `undefined` → `string` but the comparison doesn't trigger properly
- The reference equality check might miss the transition

### Issue 2: useMemo Dependencies
The `lineSelectedTokenCount` useMemo (lines 512-532) depends on:
- `fileData?.content` 
- But if the component doesn't re-render when content loads, this never recalculates

### Issue 3: State Timing
There's a timing issue where:
- `isLoadingForLines` is set to true
- Content loads and `fileData` updates
- But `isLoadingForLines` might still be true, causing "Counting..." to display
- The loading state cleanup might not happen properly

## Implementation Plan

### Phase 1: Immediate Fix - Force Re-render Detection

**File: `src/components/tree-item.tsx`**

1. **Strengthen memo comparison** (lines 360-377):
   ```typescript
   const hasFileDataChanged = (
     prevFileData: TreeNode['fileData'],
     nextFileData: TreeNode['fileData']
   ): boolean => {
     if ((prevFileData && !nextFileData) || (!prevFileData && nextFileData)) return true;
     
     if (prevFileData && nextFileData) {
       return prevFileData.tokenCount !== nextFileData.tokenCount ||
              prevFileData.isCountingTokens !== nextFileData.isCountingTokens ||
              prevFileData.isContentLoaded !== nextFileData.isContentLoaded ||
              prevFileData.isBinary !== nextFileData.isBinary ||
              prevFileData.isSkipped !== nextFileData.isSkipped ||
              // CRITICAL: Ensure content changes are detected properly
              (prevFileData.content !== nextFileData.content) ||
              // Also check for content loading state transitions
              (!prevFileData.content && nextFileData.content) ||
              (prevFileData.content && !nextFileData.content);
     }
     
     return false;
   };
   ```

2. **Add debug logging** to track state changes:
   ```typescript
   // Add to lineSelectedTokenCount useMemo
   const lineSelectedTokenCount = useMemo(() => {
     console.log('TreeItem: Calculating lineSelectedTokenCount', {
       path: node.path,
       isPartiallySelected: state.isPartiallySelected,
       hasContent: !!fileData?.content,
       contentLength: fileData?.content?.length,
       lineRanges: state.selectedFile?.lines
     });
     
     // ... existing calculation logic
   }, [type, state.isPartiallySelected, state.selectedFile, fileData?.content]);
   ```

3. **Fix loading state management** (lines 508, 561):
   ```typescript
   // Replace isLoadingForLines with more precise state tracking
   const [contentLoadingState, setContentLoadingState] = useState<'idle' | 'loading' | 'loaded'>('idle');
   
   // Update useEffect to manage state properly
   useEffect(() => {
     if (state.isPartiallySelected && state.selectedFile?.lines?.length && 
         !fileData?.isContentLoaded && !fileData?.isCountingTokens && 
         loadFileContent && type === 'file' && contentLoadingState === 'idle') {
       
       setContentLoadingState('loading');
       loadFileContent(path).then(() => {
         setContentLoadingState('loaded');
       }).catch(() => {
         setContentLoadingState('idle');
       });
     }
   }, [state.isPartiallySelected, state.selectedFile, fileData?.isContentLoaded, 
       fileData?.isCountingTokens, loadFileContent, type, path, contentLoadingState]);
   ```

### Phase 2: Alternative Approach - Direct State Access

If Phase 1 doesn't work, implement a more direct approach:

**File: `src/components/tree-item.tsx`**

1. **Add direct file data access**:
   ```typescript
   // Add to TreeItemProps interface
   interface TreeItemProps {
     // ... existing props
     allFiles: FileData[]; // Add direct access to all files
   }
   
   // In useTreeItemState hook, use direct file lookup
   const useTreeItemState = (
     node: TreeNode,
     selectedFiles: SelectedFileReference[],
     allFiles: FileData[], // Add this parameter
     loadFileContent?: (filePath: string) => Promise<void>,
     folderSelectionCache?: DirectorySelectionCache
   ) => {
     // Get fresh file data directly from allFiles instead of node.fileData
     const freshFileData = allFiles.find(f => f.path === node.path);
     const fileData = freshFileData || node.fileData;
     
     // ... rest of the logic using fileData
   };
   ```

2. **Update parent components** to pass `allFiles`:
   - `src/components/virtualized-tree.tsx`
   - `src/components/sidebar.tsx`

### Phase 3: Fallback - Remove Memo Optimization

If both approaches fail, temporarily disable memo optimization:

**File: `src/components/tree-item.tsx`**

1. **Replace memo with regular component**:
   ```typescript
   // Change from:
   const TreeItem = memo(({ ... }) => { ... }, areEqual);
   
   // To:
   const TreeItem = ({ ... }) => { ... };
   // Remove areEqual function entirely
   ```

2. **Add performance monitoring** to measure impact
3. **Re-implement selective memo** once the core issue is resolved

## Testing Strategy

### Test Cases

1. **Primary Test Case**:
   - Select lines 10-20 in a file
   - Verify tree shows token count immediately (not "Counting...")
   - Verify token count matches file card

2. **Edge Cases**:
   - Select full file, then partial → Should work
   - Select partial, then full → Should work  
   - Select multiple line ranges → Should work
   - Switch between files with line selections → Should work

3. **Performance Tests**:
   - Large files with many line ranges
   - Many files selected simultaneously
   - Rapid selection changes

### Debug Steps

1. **Add console logging** to track:
   - When `lineSelectedTokenCount` is calculated
   - When `fileData.content` changes
   - When component re-renders
   - Values of all relevant state

2. **Use React DevTools** to verify:
   - TreeItem re-renders when expected
   - Props are changing correctly
   - Memo is not blocking necessary updates

3. **Test without virtualization** temporarily to isolate the issue

## Success Criteria

- ✅ Tree shows token count immediately after selecting line ranges
- ✅ No "Counting..." stuck state
- ✅ Token count in tree matches file card
- ✅ Works consistently without needing other actions
- ✅ No performance regression
- ✅ Works with all file types and selection patterns

## Risk Assessment

**Low Risk**: Phase 1 (strengthen memo comparison)
**Medium Risk**: Phase 2 (direct state access) - requires prop threading
**High Risk**: Phase 3 (remove memo) - potential performance impact

## Implementation Order

1. Start with Phase 1 - it's the most targeted fix
2. If Phase 1 doesn't work, implement Phase 2
3. Use Phase 3 only as a last resort for immediate fix while working on a better solution

The root cause is almost certainly in the memo comparison not properly detecting content changes, so Phase 1 should resolve the issue.
