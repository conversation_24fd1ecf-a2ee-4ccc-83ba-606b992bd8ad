# Token-Count “Counting…” Bug – Investigation & Implementation Plan  
*(TreeItem never re-renders after partial line selection)*

## 1  Problem recap  
When the user selects **specific line ranges** inside a file:  
* `TreeItem` (sidebar) shows **“Counting…” forever**.  
* `FileCard` (main panel) shows **correct token count immediately**.  
* Clicking elsewhere (forcing a React update) finally reveals the token count in the tree.  
Therefore the counting logic works, but the tree does **not re-render** when `fileData.content` becomes available.

---

## 2  Potential root causes (brain-storm, 7 candidates)  
| # | Suspect area | Why it could break |
|---|--------------|--------------------|
| A | **`areEqual` memo comparator** in `tree-item.tsx` | If it returns `true` even though `fileData.content` was populated, React skips re-render. |
| B | **`useTreeItemState` internal state** (`isLoadingForLines`, `localTokenCount`) | A state variable may not change → no render. |
| C | **`estimateTokenCount` for ranges** | Miscount → `localTokenCount` unchanged so UI still shows “Counting…”. |
| D | **`updateFileWithContent` path** in `use-app-state` | Maybe `fileData` reference identity changes but `tree-item` still holds stale prop reference. |
| E | **Virtualized tree re-render policy** (`virtualized-tree.tsx`) | Off-screen nodes not forced to update. |
| F | **Selection mutation** (`use-file-selection-state`) | If `updateSelectedFile` mutates object instead of new reference, memo sees no change. |
| G | **Async race** – `loadFileContent` finishes after `isLoadingForLines` resets; memo dependencies exclude the right var.

---

## 3  Most plausible culprits (narrowed)  
1. **Suspect A – Memo comparator (`areEqual`)**  
   *Line 371–375 already adds `prevFileData.content !== nextFileData.content`.*  
   But `fileData` itself is mutated in `setAllFiles(prev => prev.map(...))` producing **new object ref**; the comparator only checks deep equality, **not reference change**. If `content` is added later but the *string value pointer hasn’t changed* (possible via cache), the comparison may still return `true`.  

2. **Suspect E – Virtualized tree**  
   `react-window` items rely on `itemData` identity; if outer parent doesn’t change its *itemData* prop when file content arrives, the row component is never asked to reconcile, so even a failing comparator won’t matter.

---

## 4  Validation logging to confirm diagnosis  
Add temporary logs:

```tsx
// tree-item.tsx – inside component body
useEffect(() => {
  console.log('[TreeItem render]', { path, contentLoaded: fileData?.isContentLoaded, tokenCount: fileData?.tokenCount });
});

useEffect(() => {
  console.log('[TreeItem compare]', node.path, '-> content changed?', hasFileDataChanged(prev.node.fileData, next.node.fileData));
}, []); // in areEqual – convert to debug version
```

```tsx
// virtualized-tree.tsx – itemData memo
console.log('[VirtualizedTree] itemData changed', itemData === prev);
```

Test workflow:  
1. Select full file → counts OK everywhere.  
2. Select lines 10-20 → watch console; verify whether TreeItem render fires.  
3. Force another action → TreeItem render fires now.

Expected: If no render happens in step 2 **Suspect E** stronger; if render happens but still “Counting…” **Suspect A/B**.

---

## 5  Proposed code fix (once diagnosis confirmed)  
1. **Ensure TreeItem receives updated props**  
   * In `use-app-state.updateFileWithContent` **create new `TreeNode` references** or bump version in `allFiles` map consumed by tree data builder to force parent list update.  
2. **Simplify comparator**  
   * Remove deep custom `areEqual` or at least always return `false` when `fileData.isContentLoaded` changed **or** when `fileData.content` gained length.  
3. **Propagate `tokenCount` via derived field**  
   * In `useTreeItemState`, set `displayTokenCount` in a stateful `useEffect` rather than pure memo; updating state guarantees render even if parent props are memoized.  
4. **Add “partial-range token count ready” event** (optional)  
   * Emit custom event from `updateFileWithContent` and listen in `TreeItem` to `forceUpdate`.

Exact patch details will depend on which validation proves correct.

---

## 6  Risk & rollback  
* Tree might re-render too often → mild perf hit but acceptable (small tree).  
* Keep original `areEqual` in git history for easy revert.

---

## 7  Next steps  
1. Insert logging listed in §4.  
2. Re-run scenario and share console output.  
3. **Confirm which suspect is correct**.  
4. Implement the fix per §5.  

---