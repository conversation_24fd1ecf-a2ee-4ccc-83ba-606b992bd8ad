/* App Header component styles */

.header {
  padding: 1rem 1.5rem;
  border-bottom: 0.0625rem solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--background-primary);
}

.header h1 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-primary);
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 100%;
}

.app-title {
  display: flex;
  align-items: center;
}

.folder-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.folder-name-container {
  display: flex;
  align-items: center;
  font-weight: 400;
  color: var(--text-secondary);
}

.selected-folder {
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  background-color: var(--hover-color);
  max-width: 18.75rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--text-secondary);
}

.folder-icon-app-title {
  color: var(--text-secondary);
  margin-right: 0.25rem;
}

/* Save button animation styles */
.button-icon.spin {
  animation: spin 1s linear infinite;
}

.button-icon.success-check {
  animation: checkmark-pop 0.3s ease-out;
}

@keyframes checkmark-pop {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}