/* Copy <PERSON>ton Component Styles */
/* Extracted from index.css lines 959-1062 */

/* Container styles */
.copy-button-container {
  display: flex;
  flex-direction: row;
  align-items: top;
  gap: 1rem;
  justify-content: center;
  background-color: var(--background-primary);
  margin-top: auto;
  padding: 1rem;
}

.copy-button-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  max-width: 30rem;
}

.copy-button-container .copy-button {
  width: 100%;
}

/* Base button styles */
.copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.375rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  border: 0.0625rem solid var(--border-color);
}

/* Primary variant */
.copy-button.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.copy-button.primary:hover:not(:disabled) {
  background-color: var(--primary-button-background);
}

/* Full width variant */
.copy-button.full-width {
  width: 100%;
  max-width: 25rem;
}

/* Copied state */
.copy-button.copied {
  background-color: var(--success-color) !important;
  border-color: var(--success-color) !important;
  color: white !important;
  transition: all 0.2s ease;
  animation: flash-success 0.3s;
}

/* Success animation */
@keyframes flash-success {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.9;
  }
}

/* Text inside button */
.copy-button-text {
  font-size: 14px;
  letter-spacing: 0.5px;
}

/* Status text (if used separately) */
.copy-status {
  opacity: 0;
  transition: opacity 0.3s ease;
  color: var(--success-color);
  font-weight: 500;
}

.copy-status.visible {
  opacity: 1;
}

/* Additional specific button style */
.copy-selected-files-btn {
  padding: 0.625rem 1rem;
  flex: 1;
}

/* Preview button styles */
.preview-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border-radius: 0.375rem;
  background-color: var(--background-secondary);
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color);
  white-space: nowrap;
}

.preview-button:hover:not(:disabled) {
  background-color: var(--hover-bg);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-button:active:not(:disabled) {
  transform: translateY(0);
}

.preview-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-button svg {
  width: 16px;
  height: 16px;
}