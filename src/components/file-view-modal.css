/* File View Modal Styles */
/* Note: Modal overlay is handled by the shared .modal-overlay class */

/* Custom cursor for drag selection */
.file-view-modal-content.selection-active pre span[style*="cursor: pointer"] {
  cursor: text !important; /* Better cursor for selection */
}

.file-view-modal {
  width: 85%;
  max-width: 1200px;
  height: 85%;
  max-height: 800px;
  background-color: var(--background-primary);
  border-radius: 8px;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.file-view-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.file-view-modal-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
}

.file-view-modal-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-secondary);
}

.file-view-modal-selection-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-view-modal-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.selection-mode-radio {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-right: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.selection-mode-radio label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  cursor: pointer;
}

.selection-mode-radio input[type="radio"] {
  margin: 0;
}

.file-view-modal-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.file-view-modal-action-btn span {
  display: none;
}

@media (min-width: 768px) {
  .file-view-modal-action-btn span {
    display: inline;
  }
}

.file-view-modal-action-btn:hover {
  background-color: var(--hover-color);
}

.file-view-modal-action-btn.active {
  background-color: var(--background-selected);
  color: var(--text-primary);
}

.file-view-modal-action-btn.primary {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border-color: var(--primary-button-background);
}

.file-view-modal-action-btn.primary:hover {
  background-color: var(--accent-blue);
}

.file-view-modal-close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: none;
  background: transparent;
  color: var(--text-primary);
  cursor: pointer;
  margin-left: .5rem;
}

.file-view-modal-close-btn:hover {
  background-color: var(--hover-color);
}

.file-view-modal-selection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  font-size: 14px;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--background-primary);
}

.file-view-modal-entire-file {
  margin-left: 8px;
  font-style: italic;
}

.file-view-modal-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.file-view-modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 16px;
  color: var(--text-secondary);
}

.file-view-modal-footer {
  padding: 12px 16px;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-primary);
  font-size: 13px;
  color: var(--text-secondary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.file-view-modal-buttons {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.file-view-modal-btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.file-view-modal-btn.cancel {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.file-view-modal-btn.cancel:hover {
  background-color: var(--hover-color);
}

.file-view-modal-btn.apply {
  background-color: var(--primary-button-background);
  color: var(--primary-button-text);
  border: none;
}

.file-view-modal-btn.apply:hover {
  background-color: var(--accent-blue);
}

/* Line number and selection styles */
.line-number {
  user-select: none;
}

.line-number.selected {
  font-weight: bold;
}

.line-number.selectable {
  cursor: pointer;
}

.line-number.selectable:hover {
  text-decoration: underline;
}