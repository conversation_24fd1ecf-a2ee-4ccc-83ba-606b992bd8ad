/* Search Bar Component Styles */
.search-bar {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  background-color: var(--background-primary);
  border-radius: 0.25rem;
  box-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-icon {
  position: absolute;
  left: 0.625rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--icon-color);
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 2;
  width: 1rem;
  height: 1rem;
}

.search-bar .search-input,
input[type="search"].search-input,
input[type="text"].search-input {
  width: 100%;
  padding: 0.5rem 2rem 0.5rem 2.25rem !important;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.75;
  outline: none;
  background-color: transparent;
  color: var(--text-primary);
}

.search-bar .search-input:focus,
input[type="search"].search-input:focus,
input[type="text"].search-input:focus {
  box-shadow: none;
}

.search-clear-btn {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0.25rem;
  color: var(--icon-color);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  z-index: 2;
}

.search-clear-btn:hover {
  background-color: var(--hover-color);
}